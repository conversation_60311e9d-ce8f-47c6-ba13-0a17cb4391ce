import 'dart:developer';
import 'dart:io';

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:saymee/core/constants/app_environment.dart';
import 'package:saymee/core/helpers/general_helper.dart';
import 'package:saymee/core/helpers/secure_storage_helper.dart';
import 'package:saymee/core/utils/utils.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'main_module.dart';
import 'main_widget.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  if (Platform.isAndroid) {
    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
  }

  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  if (Platform.isIOS) {
    await SecureStorageHelper.init();
  } else {
    try {
      await Firebase.initializeApp();
    } catch (e) {
      Utils.debugLogError(e);
    }
  }

  await GeneralHelper.init();

  await dotenv.load(fileName: AppEnvironment.envFileName);

  final sharedPreferences = await SharedPreferences.getInstance();

  runApp(ModularApp(
    module: MainModule(sharedPreferences: sharedPreferences),
    child: const MainWidget(),
  ));
}

Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  log('[FcmService] onBackgroundMessage: ${message.toMap()}');
}
